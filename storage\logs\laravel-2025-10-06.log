[2025-10-06 21:19:34] local.ERROR: Undefined variable $currentStep {"view":{"view":"C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\livewire\\admin\\hotel\\edit.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1973184130 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1907</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1973184130\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","hotel":"<pre class=sf-dump id=sf-dump-1286665415 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Hotel</span> {<a class=sf-dump-ref>#1948</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">hotels</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>nombre_personne</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>superficie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_max</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">show_url</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">types_lit</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">services</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"23 characters\">equipements_hebergement</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"22 characters\">equipements_salle_bain</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"19 characters\">equipements_cuisine</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">types_hebergement</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">caracteristiques</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"15 characters\">public_edit_url</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>annonce</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Annonce
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Annonce</span></span> {<a class=sf-dump-ref>#2389</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">annonces</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:15</span> [ &#8230;15]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">nombre_chambre</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">nombre_personne</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">superficie</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_min</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_max</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">nombre_salles_bain</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1286665415\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":13,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $currentStep at C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\livewire\\admin\\hotel\\edit.blade.php:9)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\public\\user\\annonce\\edit\\hotel.blade.php(20): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\c8ee24d94c5a581e00ffd9c7fd1afe82.php(42): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\livewire\\admin\\hotel\\edit.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1138303269 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1907</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1138303269\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","hotel":"<pre class=sf-dump id=sf-dump-724618954 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Hotel</span> {<a class=sf-dump-ref>#1948</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">hotels</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>nombre_personne</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>superficie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_max</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">show_url</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">types_lit</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">services</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"23 characters\">equipements_hebergement</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"22 characters\">equipements_salle_bain</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"19 characters\">equipements_cuisine</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">types_hebergement</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">caracteristiques</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"15 characters\">public_edit_url</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>annonce</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Annonce
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Annonce</span></span> {<a class=sf-dump-ref>#2389</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">annonces</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:15</span> [ &#8230;15]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">nombre_chambre</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">nombre_personne</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">superficie</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_min</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_max</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">nombre_salles_bain</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-724618954\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":13,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $currentStep at C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\livewire\\admin\\hotel\\edit.blade.php:9)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\public\\user\\annonce\\edit\\hotel.blade.php(20): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\c8ee24d94c5a581e00ffd9c7fd1afe82.php(42): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\admin\\annonce\\reference-select-component.blade.php","data":[]},"userId":13,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unclosed '(' does not match '}' at C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\admin\\annonce\\reference-select-component.blade.php:14)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\31f7915e3e5540997b89ea078c1f66ba.php:27)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php(181): Illuminate\\View\\View->render()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\admin\\annonce\\reference-select-component.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1251981977 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1907</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1251981977\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","hotel":"<pre class=sf-dump id=sf-dump-1405495492 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Hotel</span> {<a class=sf-dump-ref>#1948</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">hotels</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>nombre_personne</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>superficie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_max</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">show_url</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">types_lit</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">services</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"23 characters\">equipements_hebergement</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"22 characters\">equipements_salle_bain</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"19 characters\">equipements_cuisine</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">types_hebergement</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">caracteristiques</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"15 characters\">public_edit_url</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>annonce</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Annonce
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Annonce</span></span> {<a class=sf-dump-ref>#2389</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">annonces</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:15</span> [ &#8230;15]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">nombre_chambre</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">nombre_personne</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">superficie</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_min</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_max</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">nombre_salles_bain</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1405495492\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":13,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unclosed '(' does not match '}' at C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\admin\\annonce\\reference-select-component.blade.php:14)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\public\\user\\annonce\\edit\\hotel.blade.php(20): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\31f7915e3e5540997b89ea078c1f66ba.php:27)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php(181): Illuminate\\View\\View->render()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#18 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\c8ee24d94c5a581e00ffd9c7fd1afe82.php(42): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\admin\\annonce\\reference-select-component.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-841414725 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1907</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-841414725\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","hotel":"<pre class=sf-dump id=sf-dump-253383213 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Hotel</span> {<a class=sf-dump-ref>#1948</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">hotels</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_personne</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>nombre_salles_bain</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>superficie</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>200</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>300</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-03 22:56:26</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>nombre_chambre</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>nombre_personne</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>superficie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_max</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">show_url</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">types_lit</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">services</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"23 characters\">equipements_hebergement</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"22 characters\">equipements_salle_bain</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"19 characters\">equipements_cuisine</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">commodites</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">types_hebergement</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">caracteristiques</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"15 characters\">public_edit_url</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>annonce</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Annonce
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Annonce</span></span> {<a class=sf-dump-ref>#2389</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">annonces</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:15</span> [ &#8230;15]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">nombre_chambre</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">nombre_personne</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">superficie</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_min</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_max</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">nombre_salles_bain</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-253383213\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":13,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unclosed '(' does not match '}' at C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\admin\\annonce\\reference-select-component.blade.php:14)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\public\\user\\annonce\\edit\\hotel.blade.php(20): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\31f7915e3e5540997b89ea078c1f66ba.php:27)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\6246071a1b0dbef2eb6c058a442d1434.php(181): Illuminate\\View\\View->render()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Hotel\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Hotel\\Edit), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Hotel\\Edit), '<div></div>')
#18 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.hotel.edi...', Array, 'lw-1412355197-0')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\c8ee24d94c5a581e00ffd9c7fd1afe82.php(42): Livewire\\LivewireManager->mount('admin.hotel.edi...', Array, 'lw-1412355197-0', Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\livewire\\admin\\bar\\edit.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-2143186843 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1931</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-2143186843\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","bar":"<pre class=sf-dump id=sf-dump-1820654827 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Bar</span> {<a class=sf-dump-ref>#1972</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"4 characters\">bars</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>type_bar</span>\" => \"<span class=sf-dump-str title=\"5 characters\">disco</span>\"
    \"<span class=sf-dump-key>capacite_accueil</span>\" => \"<span class=sf-dump-str>3</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>123</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>1234</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-05 21:40:22</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-05 21:40:22</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>type_bar</span>\" => \"<span class=sf-dump-str title=\"5 characters\">disco</span>\"
    \"<span class=sf-dump-key>capacite_accueil</span>\" => \"<span class=sf-dump-str>3</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => <span class=sf-dump-num>123</span>
    \"<span class=sf-dump-key>prix_max</span>\" => <span class=sf-dump-num>1234</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-05 21:40:22</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-10-05 21:40:22</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>updated_by</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>deleted_by</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type_bar</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>capacite_accueil</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_min</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>prix_max</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Stevebauman\\Purify\\Casts\\PurifyHtmlOnGet</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">show_url</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"24 characters\">equipements_vie_nocturne</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">commodites_vie_nocturne</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">caracteristiques</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"15 characters\">public_edit_url</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>annonce</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Annonce
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Annonce</span></span> {<a class=sf-dump-ref>#2154</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">annonces</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:15</span> [ &#8230;15]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">type_bar</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">capacite_accueil</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_min</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">prix_max</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">userstamping</span>: <span class=sf-dump-const>true</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1820654827\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":13,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $list_commodites_vie_nocturne at C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\livewire\\admin\\bar\\edit.blade.php:108)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Bar\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Bar\\Edit), Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Bar\\Edit), '<div></div>')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.bar.edit', Array, 'lw-2005086920-0')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\resources\\views\\public\\user\\annonce\\edit\\bar.blade.php(21): Livewire\\LivewireManager->mount('admin.bar.edit', Array, 'lw-2005086920-0', Array, Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\3c5623fae0467be9863b1bd3ff0c6591.php:153)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\3c5623fae0467be9863b1bd3ff0c6591.php(153): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\Admin\\Bar\\Edit->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\Admin\\Bar\\Edit), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Admin\\Bar\\Edit), '<div></div>')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('admin.bar.edit', Array, 'lw-2005086920-0')
#14 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\storage\\framework\\views\\c99ff7cd1100c226f360cafe30e3f023.php(43): Livewire\\LivewireManager->mount('admin.bar.edit', Array, 'lw-2005086920-0', Array, Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\lenovo\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Professionnel.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Professionnel->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\app\\Http\\Middleware\\Auth.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Auth->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\spatie\\laravel-cookie-consent\\src\\CookieConsentMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\CookieConsent\\CookieConsentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\GitHub\\numdoc-annonce\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>