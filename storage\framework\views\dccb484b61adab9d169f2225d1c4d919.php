<div class="form-template">
    <form wire:submit.prevent="store">
        <?php echo csrf_field(); ?>
        <div class="card-body pt-4">
                <?php
                    $steps = ['Entreprise', 'Location', 'Véhicule', 'Spécifications', 'Images'];
                    $icons = ['fa-building', 'fa-map-marker-alt', 'fa-car', 'fa-cogs', 'fa-images'];
                ?>
            <!-- Stepper -->
            <?php if (isset($component)) { $__componentOriginal97a139cad0f6825a497afc64348ab0ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97a139cad0f6825a497afc64348ab0ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.form-stepper','data' => ['steps' => $steps,'currentStep' => $currentStep,'icons' => $icons]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.form-stepper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'icons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icons)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $attributes = $__attributesOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $component = $__componentOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__componentOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>

            <!-- Step 1: Company Information -->
            <div class="step-content <?php echo e($currentStep == 0 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.entreprise-template', [
                        'entreprises' => $entreprises,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>

            <!-- Step 2: Location -->
            <div class="step-content <?php echo e($currentStep == 1 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.location-template', [
                        'pays' => $pays,
                        'villes' => $villes,
                        'quartiers' => $quartiers,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="d-flex justify-content-end mt-4">
                    <button type="button" class="btn theme-btn" wire:click="nextStep">
                        Continuer
                    </button>
                </div>
            </div>


            <!-- Step 2: Vehicle Details -->
            <div class="step-content <?php echo e($currentStep == 2 ? '' : 'd-none'); ?> m-0">
                <div class="row align-items-start">
                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Marque
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select id="marque" class="form-control" data-nom="marque_id" wire:model.lazy='marque_id' required>
                                <option value="">-- Sélectionner --</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $list_marques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $marque): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($marque->id); ?>"><?php echo e($marque->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['marque'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Modèle
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select class="form-control" data-nom="modele_id" wire:model.lazy='modele_id' required>
                                <option value="">-- Sélectionner --</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $list_modeles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $modele): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($modele->id); ?>"><?php echo e($modele->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['modele_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Année</h3>

                            <input class="form-control" type="number" placeholder="" wire:model.defer='annee'>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['annee'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Kilométrage</h3>

                            <input class="form-control montant-format" type="text" placeholder="" wire:model.defer='kilometrage'>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['kilometrage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Nombre de places
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <input class="form-control" type="number" placeholder="" wire:model.defer='nombre_places'>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['nombre_places'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Nombre de portes
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select class="form-control" data-nom="nombre_portes" wire:model.lazy='nombre_portes' required>
                                <option value="">-- Sélectionner --</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['nombre_portes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <?php echo $__env->make('admin.annonce.price-component', [
                        'min' => true,
                        'required' => true,
                        'withTitle' => false,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.price-component', [
                        'min' => false,
                        'withTitle' => false,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                </div>
                <div class="d-flex justify-content-between my-4">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button type="button" class="btn theme-btn" wire:click="nextStep">
                        Continuer
                    </button>

                </div>

            </div>

            <!-- Step 3: Specifications -->
            <div class="step-content <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Type de moteur
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select id="carburant" class="form-control" data-nom="carburant" wire:model.defer='carburant' required>
                                <option value="">-- Sélectionner --</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $list_types_carburant; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item->valeur); ?>"><?php echo e($item->valeur); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['carburant'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Boite de vitesses
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select id="boite_vitesses" class="form-control" data-nom="boite_vitesses" wire:model.defer='boite_vitesses' required>
                                <option value="">-- Sélectionner --</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $list_boites_vitesse; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item->valeur); ?>"><?php echo e($item->valeur); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['boite_vitesses'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>

                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.description-component', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>

                <div class="row align-items-start">

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Type de voiture',
                        'name' => 'types_vehicule',
                        'options' => $list_types_vehicule,
                        'required' => true,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Options et accessoires',
                        'name' => 'equipements_vehicule',
                        'options' => $list_equipements_vehicule,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Conditions de location',
                        'name' => 'conditions_location',
                        'options' => $list_conditions_location,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                </div>

                <!-- <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                            Retour
                        </button>
                        <button type="button" class="btn theme-btn" wire:click="nextStep">
                            Continuer
                        </button>
                    </div> -->
                <div class="d-flex justify-content-between my-4" style="height: 100%;">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button type="button" class="btn theme-btn" wire:click="nextStep">
                        Continuer
                    </button>
                </div>
            </div>

            <!-- Step 4: Gallery (don't ask why step 4 before 2) (and dont move it) -->
            <div class="step-content <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                <div class="row">
                    <?php echo $__env->make('admin.annonce.create-galery-component', [
                        'galery' => $galerie,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="d-flex justify-content-between <?php echo e($currentStep == 4 ? '' : 'd-none'); ?> my-4">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button id="submit-btn" class="btn theme-btn" type="submit" wire:loading.attr='disabled'>
                        <i class="fa fa-save fa-lg"></i>
                        Enregistrer
                    </button>
                </div>
            </div>
        </div>

    </form>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/location-vehicule/create.blade.php ENDPATH**/ ?>