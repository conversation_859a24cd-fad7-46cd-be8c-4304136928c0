
<div class="form-template">
    <form wire:submit.prevent="update">
        <?php echo csrf_field(); ?>
        <?php
            $steps = ['Entreprise', 'Location', 'Description', 'Options', 'Images'];
            $icons = ['fa-briefcase', 'fa-map-marker-alt', 'fa-info-circle', 'fa-building', 'fa-image'];
        ?>
        <?php if (isset($component)) { $__componentOriginal97a139cad0f6825a497afc64348ab0ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97a139cad0f6825a497afc64348ab0ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.form-stepper','data' => ['steps' => $steps,'currentStep' => $currentStep,'icons' => $icons,'nextDisabled' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.form-stepper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'icons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icons),'nextDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $attributes = $__attributesOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $component = $__componentOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__componentOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
        <div class="card-body pt-4">

                    <!-- Step 1: Entreprise -->
                <div class="step-content <?php echo e($currentStep == 0 ? '' : 'd-none'); ?>">
                        <div class="row paddingLeft align-items-start">
                            <?php echo $__env->make('admin.annonce.entreprise-template', [
                                'entreprises' => $entreprises,
                            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>
                <!-- Step 2: Location -->
                <div class="step-content <?php echo e($currentStep == 1 ? '' : 'd-none'); ?>">                   
                    <div class="row paddingLeft align-items-start">
                        <?php echo $__env->make('admin.annonce.location-template', [
                            'pays' => $pays,
                            'villes' => $villes,
                            'quartiers' => $quartiers,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>


            <!-- Step 2: Description -->
            <div class="step-content <?php echo e($currentStep == 2 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3 class="required">Statut</h3>
                            <h4>Indiquez si l'annonce est active ou inactive</h4>
                            <select class="form-control" name="is_active" wire:model.defer='is_active' required>
                                <option value="1">Actif</option>
                                <option value="0">Inactif</option>
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <?php echo $__env->make('admin.annonce.price-component', [
                        'min' => true,
                        'required' => true,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.price-component', [
                        'min' => false,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="row align-items-start">
                    <div class="col-md-4 col-xs-12 nombre-salles-bain p-0">
                        <div class="col">
                            <h3>Nombre de salle de bain</h3>
                            <h4>Indiquez le nombre de salle de bain</h4>
                            <input class="form-control" name="nombre_salles_bain" type="number" placeholder="" wire:model.defer='nombre_salles_bain'>
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 nombre-personnes p-0">
                        <div class="col">
                            <h3>Nombre de personnes</h3>
                            <h4>Indiquez le nombre de personnes</h4>
                            <input class="form-control" name="nombre_personne" type="number" placeholder="" wire:model.defer='nombre_personne'>
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 nombre-chambres p-0">
                        <div class="col">
                            <h3>Nombre de chambre
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>
                            <h4>Indiquez le nombre de chambres</h4>
                            <input class="form-control" name="nombre_chambre" type="number" placeholder="" wire:model.defer='nombre_chambre' required>
                        </div>
                    </div>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>

            <!-- Step 3: Options -->
            <div class="step-content <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
                <div class="row paddingLeft align-items-start">
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Type d\'hebergement',
                        'name' => 'types_hebergement',
                        'options' => $list_types_hebergement,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Type de lit',
                        'name' => 'types_lit',
                        'options' => $list_types_lit,
                        'required' => true,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Commodités hébergement',
                        'name' => 'commodites',
                        'options' => $list_commodites,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="row paddingLeft align-items-start">
                    
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Services proposés',
                        'name' => 'services',
                        'options' => $list_services,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Equipements d\'hébergement',
                        'name' => 'equipements_herbegement',
                        'options' => $list_equipements_herbegement,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Accessoires de cuisine',
                        'name' => 'equipements_cuisine',
                        'options' => $list_equipements_cuisine,
                        'required' => true,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="row paddingLeft align-items-start">
                    
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Equipements de salle de bain',
                        'name' => 'equipements_salle_bain',
                        'options' => $list_equipements_salle_bain,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('admin.annonce.description-component', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>

            <!-- Step 4: Images -->
                <div class="step-content <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.edit-galery-component', [
                        'galerie' => $galerie,
                        'old_galerie' => $old_galerie,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php echo $__env->make('admin.annonce.edit-validation-buttons', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>            
        
                
    </form>
</div>


<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            $('#submit-btn').click(function() {
                var description = $('.ql-editor').html();
                window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('description', description);
            });




        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/hotel/edit.blade.php ENDPATH**/ ?>