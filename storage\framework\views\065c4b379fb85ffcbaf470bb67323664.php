 <?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['entreprises']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['entreprises']); ?>
<?php foreach (array_filter((['entreprises']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
                    
                     <div class="row align-items-start">
                        <div class="col-md-4 col-sm-12 p-0">
                            <div class="col">
                                <h3>Entreprise
                                    <b style="color: red; font-size: 100%;">*</b>
                                </h3>
                                
                                <select class="form-control" name="entreprise_id" data-nom="entreprise_id" wire:model.defer='entreprise_id' required>
                                    <option value="">-- Sélectionner --</option>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $entreprises; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entreprise): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($entreprise->id); ?>"><?php echo e($entreprise->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </select>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['entreprise_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-12 p-0">
                            <div class="col">
                                <h3>Titre
                                    <b style="color: red; font-size: 100%;">*</b>
                                </h3>
                                
                                <input class="form-control" name="nom" data-nom="nom" type="text" placeholder="" wire:model.defer='nom' required>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-12 p-0">
                            <div class="col">
                                <h3>Date de validité
                                    <b style="color: red; font-size: 100%;">*</b>
                                </h3>
                                
                                <input class="form-control" name="date_validite" data-nom="date_validite" type="date" placeholder="" disabled wire:model.defer='date_validite' required>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['date_validite'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>
                    <?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/admin/annonce/entreprise-template.blade.php ENDPATH**/ ?>