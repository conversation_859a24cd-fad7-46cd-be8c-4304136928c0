<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['title', 'name', 'options', 'required' => false]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['title', 'name', 'options', 'required' => false]); ?>
<?php foreach (array_filter((['title', 'name', 'options', 'required' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="col-md-4 col-xs-12">
    <div wire:ignore style="padding: 0 2rem;">
        <h3 class=""><?php echo e($title); ?>

            <!--[if BLOCK]><![endif]--><?php if($required): ?>
                <b style="color: red; font-size: 100%;">*</b>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </h3>

        <div class="custom-select-container" x-data="{
            open: false,
            isMobile: window.innerWidth < 768,
            selected: <?php echo \Illuminate\Support\Js::from($this->$name ?? [])->toHtml() ?>,
            init() {
                window.addEventListener('resize', () => {
                    this.isMobile = window.innerWidth < 768;
                });
            },
            toggleOption(id) {
                if (Array.isArray(this.selected)) {
                    if (this.selected.includes(id)) {
                        this.selected = this.selected.filter(item => item !== id);
                    } else {
                        this.selected = [...this.selected, id];
                    }
                } else {
                    this.selected = [id];
                }
                // Send the updated value to Livewire component immediately
                window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('<?php echo e($name); ?>', this.selected);
            },
            isSelected(id) {
                return Array.isArray(this.selected) && this.selected.includes(id);
            },
            getSelectedLabels() {
                return <?php echo \Illuminate\Support\Js::from($options)->toHtml() ?>.filter(option =>
                    Array.isArray(this.selected) && this.selected.includes(option.id)
                ).map(option => option.valeur);
            }
        }" @click.away="open = false">
            <!-- Selected items display -->
            <div
                class="form-control select-display"
                @click="open = true"
                :class="{ 'has-selections': selected && selected.length > 0 }"
            >
                <template x-if="selected && selected.length > 0">
                    <div class="selected-items">
                        <template x-for="(label, index) in getSelectedLabels()" :key="index">
                            <div class="selected-tag">
                                <span x-text="label"></span>
                                <button type="button" class="remove-tag" @click.stop="toggleOption(<?php echo \Illuminate\Support\Js::from($options)->toHtml() ?>.find(o => o.valeur === label).id)">×</button>
                            </div>
                        </template>
                    </div>
                </template>
                <div class="select-arrow" :class="{ 'open': open }">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </div>
            </div>

            <!-- Desktop dropdown -->
            <div
                x-show="open && !isMobile"
                class="select-dropdown"
                x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="opacity-0 transform scale-95"
                x-transition:enter-end="opacity-100 transform scale-100"
                x-transition:leave="transition ease-in duration-150"
                x-transition:leave-start="opacity-100 transform scale-100"
                x-transition:leave-end="opacity-0 transform scale-95"
                style="display: none;"
            >
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div
                        class="select-option"
                        @click.stop="toggleOption(<?php echo e($option->id); ?>); $event.preventDefault();"
                        :class="{ 'selected': isSelected(<?php echo e($option->id); ?>) }"
                    >
                        <?php echo e($option->valeur); ?>

                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Mobile modal -->
            <div
                x-show="open && isMobile"
                class="select-modal-backdrop"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                style="display: none;"
            >
                <div
                    class="select-modal"
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 transform translate-y-10"
                    x-transition:enter-end="opacity-100 transform translate-y-0"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 transform translate-y-0"
                    x-transition:leave-end="opacity-0 transform translate-y-10"
                    @click.stop
                >
                    <div class="select-modal-header">
                        <h3><?php echo e($title); ?></h3>
                        <button type="button" class="close-modal" @click="open = false">×</button>
                    </div>
                    <div class="select-modal-body">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="select-option"
                                @click.stop="toggleOption(<?php echo e($option->id); ?>)"
                                :class="{ 'selected': isSelected(<?php echo e($option->id); ?>) }"
                            >
                                <?php echo e($option->valeur); ?>

                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    <div class="select-modal-footer">
                        <button type="button" class="btn theme-btn" @click="open = false">Confirmer</button>
                    </div>
                </div>
            </div>

            <!-- Hidden actual select for form submission -->
            <select 
                class="hidden-select" 
                multiple 
                name="<?php echo e($name); ?>[]" 
                x-model="selected"
                data-nom="<?php echo e($name); ?>"
            >
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($option->id); ?>"><?php echo e($option->valeur); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
    </div>

    <div class="px-2">
        <!--[if BLOCK]><![endif]--><?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <span class="text-danger"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>

<style>
.custom-select-container {
    position: relative;
    width: 100%;
}

.select-display {
    display: flex;
    align-items: center;
    min-height: 38px;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: #fff;
    cursor: pointer;
    position: relative;
}

.select-display.has-selections {
    height: auto;
    min-height: 38px;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    width: 100%;
}

.selected-tag {
    display: inline-flex;
    align-items: center;
    background-color: #e9ecef;
    border-radius: 4px;
    padding: 2px 8px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.remove-tag {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 16px;
    line-height: 1;
    padding: 0 0 0 5px;
    cursor: pointer;
}

.placeholder {
    color: #fff;
}

.select-arrow {
    margin-left: auto;
    transition: transform 0.2s;
}

.select-arrow.open {
    transform: rotate(180deg);
}

.select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 250px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    margin-top: 2px;
    height: 100px;
}

.select-option {
    padding: 8px 12px;
    cursor: pointer;
}

.select-option:hover {
    background-color: #f8f9fa;
}

.select-option.selected {
    background-color: #de6600;
    color: white;
}

.hidden-select {
    display: none;
}

/* Mobile modal styles */
.select-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.select-modal {
    background-color: #fff;
    border-radius: 10px 10px 0 0;
    width: 100%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.select-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
}

.select-modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    line-height: 1;
    cursor: pointer;
}

.select-modal-body {
    overflow-y: auto;
    max-height: 60vh;
}

.select-modal-footer {
    padding: 15px;
    border-top: 1px solid #dee2e6;
    text-align: center;
}

.btn-confirm {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
}
</style>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/admin/annonce/reference-select-component.blade.php ENDPATH**/ ?>