<?php $__env->startSection('title', 'Ajouter une Boîte de nuit'); ?>

<?php $__env->startSection('content'); ?>


    <?php
        $breadcrumbs = [
            ['route' => 'accueil', 'label' => 'Accueil'],
            ['route' => 'public.annonces.create', 'label' => 'Déposer une annonce'],
            ['label' => 'Boîte de nuit'],
        ];
    ?>

    <?php if (isset($component)) { $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcumb','data' => ['backgroundImage' => ''.e(asset('assets_client/img/banner/image-1.jpg')).'','showTitle' => true,'title' => 'Ajouter une boîte de nuit','breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['backgroundImage' => ''.e(asset('assets_client/img/banner/image-1.jpg')).'','showTitle' => true,'title' => 'Ajouter une boîte de nuit','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $attributes = $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $component = $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>

    <div >
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.boite-de-nuit.create');

$__html = app('livewire')->mount($__name, $__params, 'lw-715088581-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.full.min.js"></script>
    <script>
        var mymap = L.map('map').setView([8.6195, 0.8248], 6);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19
        }).addTo(mymap);

        var marker;

        mymap.on('click', function(e) {
            if (marker) {
                mymap.removeLayer(marker); // Supprimez le marqueur existant s'il y en a un.
            }

            marker = L.marker(e.latlng).addTo(mymap);
            var lat = e.latlng.lat;
            var lon = e.latlng.lng;



            Livewire.dispatch('setLocation', [{
                lon,
                lat
            }]);
        });
    </script>

    <script>
        $('.select2').each(function() {
            $(this).select2({
                theme: 'bootstrap-5',
                dropdownParent: $(this).parent(),
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.public.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/public/user/annonce/create/boite-de-nuit.blade.php ENDPATH**/ ?>