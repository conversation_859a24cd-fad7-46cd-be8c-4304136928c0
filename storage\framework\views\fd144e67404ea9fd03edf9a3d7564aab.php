<div class="form-template">
    <form wire:submit.prevent="store" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <div class="card-body pt-4">
                <?php
                    $steps = ['Entreprise', 'Location', 'Informations', 'Menus', 'Images'];
                    $icons = ['fa-building', 'fa-map-marker-alt', 'fa-info-circle', 'fa-utensils', 'fa-images'];
                ?>
                
                <?php if (isset($component)) { $__componentOriginal97a139cad0f6825a497afc64348ab0ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97a139cad0f6825a497afc64348ab0ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.form-stepper','data' => ['steps' => $steps,'currentStep' => $currentStep,'icons' => $icons]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.form-stepper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'icons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icons)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $attributes = $__attributesOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $component = $__componentOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__componentOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>

            <!-- Step 1: Entreprise -->
            <div class="step-content <?php echo e($currentStep == 0 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                        <?php echo $__env->make('admin.annonce.entreprise-template', [
                            'entreprises' => $entreprises,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>

            </div>


            <!-- Step 2: Location -->
            <div class="step-content <?php echo e($currentStep == 1 ? '' : 'd-none'); ?>">
                    <div class="row align-items-start">
                        <?php echo $__env->make('admin.annonce.location-template', [
                            'pays' => $pays,
                            'villes' => $villes,
                            'quartiers' => $quartiers,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>


            <!-- Step 2: Informations -->
            <div class="step-content <?php echo e($currentStep == 2 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.description-component', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Équipements',
                        'name' => 'equipements_restauration',
                        'options' => $list_equipements_restauration,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Services proposés',
                        'name' => 'services',
                        'options' => $list_services,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 3]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 3]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>

            <!-- Step 3: Menus -->
            <div class="step-content <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <div class="col-md-12 col-sm-12 p-0">
                        <div class="col produits">
                            <h3>Menus (<?php echo e(count($produits)); ?>)
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>
                            <h4>Carte de menus</h4>
                            <div id="produits-container">
                                <!-- Produit 1 par défaut -->
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $produits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div id="produit-item-<?php echo e($index + 1); ?>" class="form-group produit-item">
                                        <div>
                                            <button class="btn btn-form" data-bs-toggle="offcanvas" data-bs-target="#produit-<?php echo e($index + 1); ?>" type="button" aria-controls="produit-<?php echo e($index + 1); ?>">
                                                Menu <?php echo e($index + 1); ?> : <?php echo e($menu['nom']); ?> <i class="fa fa-pencil"></i>
                                            </button>
                                        </div>
                                        <div id="produit-<?php echo e($index + 1); ?>" class="offcanvas offcanvas-end" data-bs-scroll="true" aria-labelledby="produit-<?php echo e($index + 1); ?>" tabindex="-1">
                                            <div class="offcanvas-header">
                                                <h5 class="offcanvas-title">Menu <?php echo e($index + 1); ?></h5>
                                                <button id="produits-close-<?php echo e($index + 1); ?>" class="btn-close text-reset" data-bs-dismiss="offcanvas" type="button" aria-label="Close"></button>
                                            </div>
                                            <div class="offcanvas-body">
                                                <div class="form-group">
                                                    <label for="produit-name-<?php echo e($index + 1); ?>">Nom<b style="color: red; font-size: 100%;">*</b></label>
                                                    <input id="produit-name-<?php echo e($index + 1); ?>" class="form-control required-field" type="text" wire:model="produits.<?php echo e($index); ?>.nom">
                                                </div>
                                                <div class="form-group">
                                                    <label for="produit-description-<?php echo e($index + 1); ?>">Accompagnements<b style="color: red; font-size: 100%;">*</b></label>
                                                    <textarea id="produit-description-<?php echo e($index + 1); ?>" class="form-control required-field" wire:model="produits.<?php echo e($index); ?>.accompagnements" rows="3"></textarea>
                                                </div>
                                                <div class="form-group">
                                                    <label for="produit-price-<?php echo e($index + 1); ?>">Prix<b style="color: red; font-size: 100%;">*</b></label>
                                                    <input id="produit-price-<?php echo e($index + 1); ?>" class="form-control required-field" type="number" wire:model="produits.<?php echo e($index); ?>.prix">
                                                </div>
                                                <div class="form-group">
                                                    <label for="form-img-produit-<?php echo e($index + 1); ?>">Image à la Une <b style="color: red; font-size: 100%;">*</b></label>
                                                    <input id="form-img-produit-<?php echo e($index + 1); ?>" class="form-control form-control-file" data-id="<?php echo e($index + 1); ?>" type="file" wire:model="produits.<?php echo e($index); ?>.image" accept="image/*">

                                                    <!--[if BLOCK]><![endif]--><?php if(!empty($produits[$index]['image'])): ?>
                                                        <img class="listing-shot-img img-responsive" src="<?php echo e($produits[$index]['image']->temporaryUrl()); ?>" alt="" style="width: 100%; height: 100px; object-fit: cover;">
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                </div>
                                                <button class="btn btn-danger delete-produit-btn mb-2" data-produit-id="<?php echo e($index + 1); ?>" type="button" wire:click="removeProduit(<?php echo e($index); ?>)">Supprimer</button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                <div class="col-md-12 col-sm-12 text-center">
                                    <span id="produit-error-message" class="text-danger"><br></span>
                                </div>
                                <!--[if BLOCK]><![endif]--><?php if($produits_error): ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($produits_error); ?></span>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.accompagnements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.prix'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <button id="add-produit-btn" class="btn btn-success btn-square" type="button"><i class="fa fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>


            

            <!-- Step 4: Images -->
            <div class="step-content <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.create-galery-component', [
                        'galery' => $galerie,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>

                <div class="d-flex justify-content-between my-4 <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button id="restaurant-form-submit" class="btn theme-btn" type="submit" style="margin-right: 30px;" wire:loading.attr="disabled">
                                <i class="fa fa-save fa-lg" style="margin-right: 10px;"></i>
                                Enregistrer
                            </button>
                </div>

            </div>
        </div>
    </form>
</div>



<script>
    window.addEventListener('console-log', event => {
        console.log(event.detail.message);
    });
</script><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/fast-food/create.blade.php ENDPATH**/ ?>