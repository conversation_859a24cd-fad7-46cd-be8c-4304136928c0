
<div class="form-template">
    <form wire:submit.prevent="update">
        <?php echo csrf_field(); ?>


        <?php
            $steps = ['Entreprise', 'Location', 'Description', 'Images'];
            $icons = ['fa-briefcase', 'fa-map-marker-alt', 'fa-info-circle', 'fa-image'];
        ?>
        
        <?php if (isset($component)) { $__componentOriginal97a139cad0f6825a497afc64348ab0ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97a139cad0f6825a497afc64348ab0ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.form-stepper','data' => ['steps' => $steps,'currentStep' => $currentStep,'icons' => $icons,'nextDisabled' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.form-stepper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'icons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icons),'nextDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $attributes = $__attributesOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $component = $__componentOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__componentOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>

            <!-- Step 1: Entreprise -->
            <div class="step-content <?php echo e($currentStep == 0 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                        <?php echo $__env->make('admin.annonce.entreprise-template', [
                            'entreprises' => $entreprises,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="col-md-4 col-xs-12 is-active p-0">
                        <div class="col">
                            <h3 class="required">Statut</h3>
                            <h4>Indiquez si l'annonce est active ou inactive</h4>
                            <select class="form-control" name="is_active" wire:model.defer='is_active' required>
                                <option value="1">Actif</option>
                                <option value="0">Inactif</option>
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>

            </div>


            <!-- Step 2: Location -->
            <div class="step-content <?php echo e($currentStep == 1 ? '' : 'd-none'); ?>">
                    <div class="row align-items-start">
                        <?php echo $__env->make('admin.annonce.location-template', [
                            'pays' => $pays,
                            'villes' => $villes,
                            'quartiers' => $quartiers,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>


        <!-- Step 2: Description -->
        <div class="step-content <?php echo e($currentStep == 2 ? '' : 'd-none'); ?>">
            <div class="row align-items-start">
                <?php echo $__env->make('admin.annonce.price-component', [
                    'min' => true,
                    'required' => true,
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <?php echo $__env->make('admin.annonce.price-component', [
                    'min' => false,
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <div class="col-md-4 col-xs-12 nombre-salles-bain p-0">
                    <div class="col">
                        <h3>Capacité d'accueil</h3>
                        <h4>Indiquez la capacité d'accueil</h4>
                        <input class="form-control" name="capacite_accueil" type="number" placeholder="" wire:model.defer='capacite_accueil' min="0">
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['capacite_accueil'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>

                <div class="col-md-4 col-xs-12 nombre-salles-bain p-0">
                    <div class="col">
                        <h3>Type de bar</h3>
                        <h4>Indiquez le type de bar</h4>
                        <input class="form-control" name="type_bar" type="text" placeholder="" wire:model.defer='type_bar' min="0">
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['type_bar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
                <div class="col-md-8 col-xs-12">
                    <?php echo $__env->make('admin.annonce.description-component', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>
            <div class="row align-items-start">
                <?php echo $__env->make('admin.annonce.reference-select-component', [
                    'title' => 'Equipements vie nocturne',
                    'name' => 'equipements_vie_nocturne',
                    'options' => $list_equipements_vie_nocturne,
                    'required' => true,
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <?php echo $__env->make('admin.annonce.reference-select-component', [
                    'title' => 'Commodités vie nocturne',
                    'name' => 'commodites_vie_nocturne',
                    'options' => $list_commodites_vie_nocturne,
                    'required' => true,
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <?php echo $__env->make('admin.annonce.reference-select-component', [
                    'title' => 'Type de musique',
                    'name' => 'types_musique',
                    'options' => $list_types_musique,
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 3]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 3]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
        </div>

        <!-- Step 3: Images -->
        <div class="step-content <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
            <div class="row align-items-start">
                <?php echo $__env->make('admin.annonce.edit-galery-component', [
                    'galerie' => $galerie,
                    'old_galerie' => $old_galerie,
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="row padd-bot-15 <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
                <?php echo $__env->make('admin.annonce.edit-validation-buttons', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>

        
    </form>
</div>


<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            $('#submit-btn').click(function() {
                var description = $('.ql-editor').html();
                window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('description', description);
            });




        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/bar/edit.blade.php ENDPATH**/ ?>